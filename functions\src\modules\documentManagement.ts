import * as functions from "firebase-functions/v1";
import * as admin from "firebase-admin";

/**
 * Approve a document (simplified without status management)
 */
const approveDocument = functions.https.onCall(async (data: any, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "User must be authenticated"
    );
  }

  try {
    const { documentId } = data;

    if (!documentId) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Document ID is required"
      );
    }

    // Check user permissions
    const userDoc = await admin
      .firestore()
      .collection("users")
      .doc(context.auth.uid)
      .get();
    const user = userDoc.data();

    if (!user || user.role !== "admin") {
      throw new functions.https.HttpsError(
        "permission-denied",
        "Only admins can approve documents"
      );
    }

    // Update document
    await admin
      .firestore()
      .collection("document-metadata")
      .doc(documentId)
      .update({
        approvedBy: context.auth.uid,
        approvedAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });

    // Log activity
    await admin
      .firestore()
      .collection("activities")
      .add({
        type: "document_approved",
        documentId: documentId,
        userId: context.auth.uid,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        details: "Document approved by admin",
      });

    return {
      success: true,
      message: "Document approved successfully",
    };
  } catch (error) {
    console.error("Error approving document:", error);
    throw new functions.https.HttpsError(
      "internal",
      `Failed to approve document: ${error}`
    );
  }
});

/**
 * Reject a document (simplified without status management)
 */
const rejectDocument = functions.https.onCall(async (data: any, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "User must be authenticated"
    );
  }

  try {
    const { documentId, reason } = data;

    if (!documentId) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Document ID is required"
      );
    }

    // Check user permissions
    const userDoc = await admin
      .firestore()
      .collection("users")
      .doc(context.auth.uid)
      .get();
    const user = userDoc.data();

    if (!user || user.role !== "admin") {
      throw new functions.https.HttpsError(
        "permission-denied",
        "Only admins can reject documents"
      );
    }

    // Update document
    await admin
      .firestore()
      .collection("document-metadata")
      .doc(documentId)
      .update({
        rejectedBy: context.auth.uid,
        rejectedAt: admin.firestore.FieldValue.serverTimestamp(),
        rejectionReason: reason || "No reason provided",
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });

    // Log activity
    await admin
      .firestore()
      .collection("activities")
      .add({
        type: "document_rejected",
        documentId: documentId,
        userId: context.auth.uid,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        details: `Document rejected by admin: ${reason || "No reason provided"}`,
      });

    return {
      success: true,
      message: "Document rejected successfully",
    };
  } catch (error) {
    console.error("Error rejecting document:", error);
    throw new functions.https.HttpsError(
      "internal",
      `Failed to reject document: ${error}`
    );
  }
});

/**
 * Bulk document operations
 */
const bulkDocumentOperations = functions.https.onCall(async (data: any, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "User must be authenticated"
    );
  }

  try {
    const { documentIds, operation, reason } = data;

    if (!documentIds || !Array.isArray(documentIds) || documentIds.length === 0) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Document IDs array is required"
      );
    }

    if (!operation || !["approve", "reject", "delete"].includes(operation)) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Valid operation (approve, reject, delete) is required"
      );
    }

    // Check user permissions
    const userDoc = await admin
      .firestore()
      .collection("users")
      .doc(context.auth.uid)
      .get();
    const user = userDoc.data();

    if (!user || user.role !== "admin") {
      throw new functions.https.HttpsError(
        "permission-denied",
        "Only admins can perform bulk document operations"
      );
    }

    const batch = admin.firestore().batch();
    const results = [];

    for (const documentId of documentIds) {
      try {
        const docRef = admin.firestore().collection("document-metadata").doc(documentId);
        
        switch (operation) {
        case "approve":
          batch.update(docRef, {
            approvedBy: context.auth.uid,
            approvedAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          break;
        case "reject":
          batch.update(docRef, {
            rejectedBy: context.auth.uid,
            rejectedAt: admin.firestore.FieldValue.serverTimestamp(),
            rejectionReason: reason || "Bulk rejection",
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          break;
        case "delete":
          batch.update(docRef, {
            isActive: false,
            deletedBy: context.auth.uid,
            deletedAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          break;
        }

        results.push({ documentId, success: true });
      } catch (error) {
        results.push({ documentId, success: false, error: String(error) });
      }
    }

    await batch.commit();

    // Log activity
    await admin
      .firestore()
      .collection("activities")
      .add({
        type: `bulk_document_${operation}`,
        userId: context.auth.uid,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        details: `Bulk ${operation} operation performed on ${documentIds.length} documents`,
        documentIds: documentIds,
      });

    return {
      success: true,
      message: `Bulk ${operation} operation completed`,
      results: results,
    };
  } catch (error) {
    console.error("Error in bulk document operations:", error);
    throw new functions.https.HttpsError(
      "internal",
      `Bulk document operation failed: ${error}`
    );
  }
});

/**
 * Delete document permanently (from both Firestore and Storage)
 * This function provides atomic deletion with proper error handling
 */
const deleteDocument = functions.https.onCall(async (data: any, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "User must be authenticated"
    );
  }

  try {
    const { documentId } = data;

    if (!documentId) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Document ID is required"
      );
    }

    console.log(`🗑️ Starting delete operation for document: ${documentId}`);

    // ADMIN-ONLY: Check user permissions
    const userDoc = await admin
      .firestore()
      .collection("users")
      .doc(context.auth.uid)
      .get();
    const user = userDoc.data();

    if (!user || user.role !== "admin") {
      throw new functions.https.HttpsError(
        "permission-denied",
        "Access denied: Only administrators can delete files"
      );
    }

    console.log(`✅ Admin permission verified for user: ${context.auth.uid}`);

    // Get document metadata from Firestore
    const docRef = admin.firestore().collection("document-metadata").doc(documentId);
    const docSnapshot = await docRef.get();

    if (!docSnapshot.exists) {
      console.log(`⚠️ Document not found in Firestore: ${documentId}`);
      throw new functions.https.HttpsError(
        "not-found",
        "Document not found in database"
      );
    }

    const documentData = docSnapshot.data();
    const fileName = documentData?.fileName || "Unknown File";
    const filePath = documentData?.filePath || "";

    console.log(`📁 Found document: ${fileName} at path: ${filePath}`);

    // ATOMIC OPERATION: Delete from both Storage and Firestore
    const bucket = admin.storage().bucket();
    let storageDeleted = false;
    let firestoreDeleted = false;

    try {
      // Step 1: Delete from Firebase Storage
      if (filePath) {
        try {
          const file = bucket.file(filePath);
          await file.delete();
          storageDeleted = true;
          console.log(`✅ Successfully deleted from Storage: ${filePath}`);
        } catch (storageError: any) {
          console.log(`⚠️ Storage deletion failed: ${storageError.message}`);

          // Try alternative storage paths
          const alternativePaths = [
            `documents/${documentData?.uploadedBy}/${fileName}`,
            `documents/${fileName}`,
            `documents/${documentId}`,
          ];

          for (const altPath of alternativePaths) {
            try {
              const altFile = bucket.file(altPath);
              await altFile.delete();
              storageDeleted = true;
              console.log(`✅ Successfully deleted from alternative path: ${altPath}`);
              break;
            } catch (altError) {
              console.log(`⚠️ Alternative path failed: ${altPath}`);
            }
          }

          if (!storageDeleted) {
            console.log(`❌ All storage deletion attempts failed for: ${fileName}`);
            // Continue with Firestore deletion even if storage fails
          }
        }
      } else {
        console.log(`⚠️ No file path found, skipping storage deletion`);
      }

      // Step 2: Delete from Firestore
      await docRef.delete();
      firestoreDeleted = true;
      console.log(`✅ Successfully deleted from Firestore: ${documentId}`);

      // Step 3: Log activity
      await admin
        .firestore()
        .collection("activities")
        .add({
          type: "document_deleted",
          documentId: documentId,
          userId: context.auth.uid,
          timestamp: admin.firestore.FieldValue.serverTimestamp(),
          details: `Document permanently deleted: ${fileName}`,
          metadata: {
            fileName: fileName,
            filePath: filePath,
            storageDeleted: storageDeleted,
            firestoreDeleted: firestoreDeleted,
          },
        });

      console.log(`✅ Delete operation completed successfully for: ${fileName}`);

      return {
        success: true,
        message: `Document "${fileName}" deleted successfully`,
        details: {
          documentId: documentId,
          fileName: fileName,
          storageDeleted: storageDeleted,
          firestoreDeleted: firestoreDeleted,
        },
      };

    } catch (operationError: any) {
      console.error(`❌ Delete operation failed: ${operationError.message}`);

      // Log failed deletion attempt
      await admin
        .firestore()
        .collection("activities")
        .add({
          type: "document_delete_failed",
          documentId: documentId,
          userId: context.auth.uid,
          timestamp: admin.firestore.FieldValue.serverTimestamp(),
          details: `Failed to delete document: ${fileName}`,
          error: operationError.message,
        });

      throw new functions.https.HttpsError(
        "internal",
        `Failed to delete document: ${operationError.message}`
      );
    }

  } catch (error: any) {
    console.error("Error in deleteDocument function:", error);

    // Re-throw HttpsError as-is
    if (error instanceof functions.https.HttpsError) {
      throw error;
    }

    // Wrap other errors
    throw new functions.https.HttpsError(
      "internal",
      `Delete operation failed: ${error.message || error}`
    );
  }
});

/**
 * Generate document report
 */
const generateDocumentReport = functions.https.onCall(async (data: any, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "User must be authenticated"
    );
  }

  try {
    // Check user permissions
    const userDoc = await admin
      .firestore()
      .collection("users")
      .doc(context.auth.uid)
      .get();
    const user = userDoc.data();

    if (!user || user.role !== "admin") {
      throw new functions.https.HttpsError(
        "permission-denied",
        "Only admins can generate document reports"
      );
    }

    const { startDate, endDate, categoryId } = data;

    let query = admin
      .firestore()
      .collection("document-metadata")
      .where("isActive", "==", true);

    if (startDate) {
      query = query.where("uploadedAt", ">=", new Date(startDate));
    }

    if (endDate) {
      query = query.where("uploadedAt", "<=", new Date(endDate));
    }

    if (categoryId) {
      query = query.where("category", "==", categoryId);
    }

    const documentsSnapshot = await query.get();
    const documents = documentsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    }));

    // Generate statistics
    const stats = {
      totalDocuments: documents.length,
      documentsByCategory: {} as Record<string, number>,
      documentsByType: {} as Record<string, number>,
      documentsByUploader: {} as Record<string, number>,
      totalSize: 0,
    };

    documents.forEach((doc: any) => {
      // Count by category
      const category = doc.category || "Uncategorized";
      stats.documentsByCategory[category] = (stats.documentsByCategory[category] || 0) + 1;

      // Count by type
      const type = getFileTypeFromName(doc.fileName || "");
      stats.documentsByType[type] = (stats.documentsByType[type] || 0) + 1;

      // Count by uploader
      const uploader = doc.uploadedBy || "Unknown";
      stats.documentsByUploader[uploader] = (stats.documentsByUploader[uploader] || 0) + 1;

      // Sum file sizes
      stats.totalSize += doc.fileSize || 0;
    });

    // Log activity
    await admin
      .firestore()
      .collection("activities")
      .add({
        type: "document_report_generated",
        userId: context.auth.uid,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        details: `Document report generated for ${documents.length} documents`,
      });

    return {
      success: true,
      report: {
        generatedAt: new Date().toISOString(),
        generatedBy: context.auth.uid,
        filters: { startDate, endDate, categoryId },
        statistics: stats,
        documents: documents,
      },
    };
  } catch (error) {
    console.error("Error generating document report:", error);
    throw new functions.https.HttpsError(
      "internal",
      `Failed to generate document report: ${error}`
    );
  }
});

// Helper function
function getFileTypeFromName(fileName: string): string {
  const extension = fileName.split(".").pop()?.toLowerCase();

  switch (extension) {
  case "pdf":
    return "PDF";
  case "doc":
  case "docx":
    return "DOC";
  case "xls":
  case "xlsx":
    return "Excel";
  case "ppt":
  case "pptx":
    return "PPT";
  case "jpg":
  case "jpeg":
  case "png":
  case "gif":
    return "Image";
  case "txt":
    return "Text";
  default:
    return "Other";
  }
}

export const documentFunctions = {
  approveDocument,
  rejectDocument,
  deleteDocument,
  bulkDocumentOperations,
  generateDocumentReport,
};
